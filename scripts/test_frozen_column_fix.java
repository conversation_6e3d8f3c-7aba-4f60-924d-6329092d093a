import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.util.HashSet;
import java.util.Set;

/**
 * 简单的验证脚本，测试 FrozenColumnTable 的修复效果
 */
public class TestFrozenColumnFix {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建测试窗口
            JFrame frame = new JFrame("FrozenColumnTable 大数据量测试");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(800, 600);
            
            // 创建大数据量的表格模型
            TestTableModel model = new TestTableModel(15000);
            JTable table = new JTable(model);
            
            // 创建过滤器测试
            TableRowSorter<TableModel> sorter = new TableRowSorter<>(model);
            table.setRowSorter(sorter);
            
            // 创建过滤器，隐藏5000-10000行
            RowFilter<Object, Object> filter = new RowFilter<Object, Object>() {
                @Override
                public boolean include(Entry<? extends Object, ? extends Object> entry) {
                    int row = Integer.parseInt(entry.getIdentifier().toString());
                    return !(row >= 5000 && row < 10000);
                }
            };
            
            // 应用过滤器
            sorter.setRowFilter(filter);
            
            // 创建滚动面板
            JScrollPane scrollPane = new JScrollPane(table);
            
            // 创建行号表格（简化版本，模拟修复后的逻辑）
            JTable rowHeaderTable = createRowHeaderTable(table);
            scrollPane.setRowHeaderView(rowHeaderTable);
            
            // 添加控制面板
            JPanel controlPanel = new JPanel();
            JButton filterButton = new JButton("应用过滤器");
            JButton clearButton = new JButton("清除过滤器");
            JLabel statusLabel = new JLabel("总行数: " + model.getRowCount() + ", 可见行数: " + table.getRowCount());
            
            filterButton.addActionListener(e -> {
                sorter.setRowFilter(filter);
                statusLabel.setText("总行数: " + model.getRowCount() + ", 可见行数: " + table.getRowCount());
                ((AbstractTableModel) rowHeaderTable.getModel()).fireTableDataChanged();
            });
            
            clearButton.addActionListener(e -> {
                sorter.setRowFilter(null);
                statusLabel.setText("总行数: " + model.getRowCount() + ", 可见行数: " + table.getRowCount());
                ((AbstractTableModel) rowHeaderTable.getModel()).fireTableDataChanged();
            });
            
            controlPanel.add(filterButton);
            controlPanel.add(clearButton);
            controlPanel.add(statusLabel);
            
            frame.add(scrollPane, BorderLayout.CENTER);
            frame.add(controlPanel, BorderLayout.SOUTH);
            
            frame.setLocationRelativeTo(null);
            frame.setVisible(true);
            
            System.out.println("测试窗口已启动");
            System.out.println("原始行数: " + model.getRowCount());
            System.out.println("过滤后行数: " + table.getRowCount());
            System.out.println("行号表格行数: " + rowHeaderTable.getRowCount());
        });
    }
    
    private static JTable createRowHeaderTable(JTable mainTable) {
        return new JTable(new AbstractTableModel() {
            @Override
            public int getRowCount() {
                // 使用主表的实际可见行数，考虑过滤器的影响
                if (mainTable.getRowSorter() != null) {
                    return mainTable.getRowCount();
                } else {
                    return mainTable.getModel().getRowCount();
                }
            }
            
            @Override
            public int getColumnCount() {
                return 1;
            }
            
            @Override
            public Object getValueAt(int rowIndex, int columnIndex) {
                // 显示过滤后的实际行号
                if (mainTable.getRowSorter() != null) {
                    int modelRow = mainTable.convertRowIndexToModel(rowIndex);
                    return modelRow + 1; // 行号从 1 开始，显示原始行号
                } else {
                    return rowIndex + 1;
                }
            }
            
            @Override
            public String getColumnName(int column) {
                return "行号";
            }
        });
    }
    
    static class TestTableModel extends AbstractTableModel {
        private final int rowCount;
        private final String[] columnNames = {"列1", "列2", "列3"};
        
        public TestTableModel(int rowCount) {
            this.rowCount = rowCount;
        }
        
        @Override
        public int getRowCount() {
            return rowCount;
        }
        
        @Override
        public int getColumnCount() {
            return columnNames.length;
        }
        
        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            return "数据" + rowIndex + "-" + columnIndex;
        }
        
        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }
    }
}
