import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;

/**
 * Simple validation script to test FrozenColumnTable fix
 */
public class TestFrozenColumnFix {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // Create test window
            JFrame frame = new JFrame("FrozenColumnTable Large Data Test");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(800, 600);
            
            // Create large data table model
            TestTableModel model = new TestTableModel(15000);
            JTable table = new JTable(model);
            
            // Create filter test
            TableRowSorter<TableModel> sorter = new TableRowSorter<>(model);
            table.setRowSorter(sorter);
            
            // Create filter to hide rows 5000-10000
            RowFilter<Object, Object> filter = new RowFilter<Object, Object>() {
                @Override
                public boolean include(Entry<? extends Object, ? extends Object> entry) {
                    int row = Integer.parseInt(entry.getIdentifier().toString());
                    return !(row >= 5000 && row < 10000);
                }
            };
            
            // Apply filter
            sorter.setRowFilter(filter);
            
            // Create scroll pane
            JScrollPane scrollPane = new JScrollPane(table);
            
            // Create row header table (simplified version, simulating fixed logic)
            JTable rowHeaderTable = createRowHeaderTable(table);
            scrollPane.setRowHeaderView(rowHeaderTable);
            
            // Add control panel
            JPanel controlPanel = new JPanel();
            JButton filterButton = new JButton("Apply Filter");
            JButton clearButton = new JButton("Clear Filter");
            JLabel statusLabel = new JLabel("Total rows: " + model.getRowCount() + ", Visible rows: " + table.getRowCount());
            
            filterButton.addActionListener(e -> {
                sorter.setRowFilter(filter);
                statusLabel.setText("Total rows: " + model.getRowCount() + ", Visible rows: " + table.getRowCount());
                ((AbstractTableModel) rowHeaderTable.getModel()).fireTableDataChanged();
            });
            
            clearButton.addActionListener(e -> {
                sorter.setRowFilter(null);
                statusLabel.setText("Total rows: " + model.getRowCount() + ", Visible rows: " + table.getRowCount());
                ((AbstractTableModel) rowHeaderTable.getModel()).fireTableDataChanged();
            });
            
            controlPanel.add(filterButton);
            controlPanel.add(clearButton);
            controlPanel.add(statusLabel);
            
            frame.add(scrollPane, BorderLayout.CENTER);
            frame.add(controlPanel, BorderLayout.SOUTH);
            
            frame.setLocationRelativeTo(null);
            frame.setVisible(true);
            
            System.out.println("Test window started");
            System.out.println("Original rows: " + model.getRowCount());
            System.out.println("Filtered rows: " + table.getRowCount());
            System.out.println("Row header rows: " + rowHeaderTable.getRowCount());
        });
    }
    
    private static JTable createRowHeaderTable(JTable mainTable) {
        return new JTable(new AbstractTableModel() {
            @Override
            public int getRowCount() {
                // Use actual visible row count of main table, considering filter effects
                if (mainTable.getRowSorter() != null) {
                    return mainTable.getRowCount();
                } else {
                    return mainTable.getModel().getRowCount();
                }
            }
            
            @Override
            public int getColumnCount() {
                return 1;
            }
            
            @Override
            public Object getValueAt(int rowIndex, int columnIndex) {
                // Display actual row number after filtering
                if (mainTable.getRowSorter() != null) {
                    int modelRow = mainTable.convertRowIndexToModel(rowIndex);
                    return modelRow + 1; // Row number starts from 1, showing original row number
                } else {
                    return rowIndex + 1;
                }
            }
            
            @Override
            public String getColumnName(int column) {
                return "Row#";
            }
        });
    }
    
    static class TestTableModel extends AbstractTableModel {
        private final int rowCount;
        private final String[] columnNames = {"Col1", "Col2", "Col3"};
        
        public TestTableModel(int rowCount) {
            this.rowCount = rowCount;
        }
        
        @Override
        public int getRowCount() {
            return rowCount;
        }
        
        @Override
        public int getColumnCount() {
            return columnNames.length;
        }
        
        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            return "Data" + rowIndex + "-" + columnIndex;
        }
        
        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }
    }
}
