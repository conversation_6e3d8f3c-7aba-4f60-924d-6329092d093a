package ui.base.table;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTable;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.table.AbstractTableModel;
import javax.swing.table.TableModel;
import javax.swing.table.TableRowSorter;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FrozenColumnTable 测试类
 * 主要测试大数据量下的滚动同步功能
 */
public class FrozenColumnTableTest {

    @Mock
    private MainModel mainModel;
    
    @Mock
    private ExcelCaseTable excelTable;
    
    private JScrollPane scrollPane;
    private FrozenColumnTable frozenColumnTable;
    private TestTableModel tableModel;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试用的表格模型，模拟大数据量
        tableModel = new TestTableModel(15000); // 15000行数据
        
        // 创建主表格
        JTable mainTable = new JTable(tableModel);
        
        // 设置 ExcelCaseTable 的模拟行为
        when(excelTable.getModel()).thenReturn(tableModel);
        when(excelTable.getRowCount()).thenReturn(tableModel.getRowCount());
        when(excelTable.getHiddenRows()).thenReturn(new HashSet<>());
        
        // 创建滚动面板
        scrollPane = new JScrollPane(excelTable);
        
        // 创建 FrozenColumnTable
        frozenColumnTable = new FrozenColumnTable(mainModel, 1, scrollPane);
    }
    
    @Test
    void testRowHeaderTableSyncWithMainTable() {
        // 测试行号表格与主表格的行数同步
        assertEquals(excelTable.getRowCount(), frozenColumnTable.getRowHeaderTable().getRowCount(),
                "行号表格的行数应该与主表格一致");
    }
    
    @Test
    void testFilterSynchronization() {
        // 模拟应用过滤器
        Set<Integer> hiddenRows = new HashSet<>();
        for (int i = 5000; i < 10000; i++) {
            hiddenRows.add(i);
        }
        when(excelTable.getHiddenRows()).thenReturn(hiddenRows);
        
        // 创建过滤器
        TableRowSorter<TableModel> sorter = new TableRowSorter<>(tableModel);
        RowFilter<Object, Object> filter = new RowFilter<Object, Object>() {
            @Override
            public boolean include(Entry<? extends Object, ? extends Object> entry) {
                int modelRow = Integer.parseInt(entry.getIdentifier().toString());
                return !hiddenRows.contains(modelRow);
            }
        };
        
        // 应用过滤器到主表格
        excelTable.setRowSorter(sorter);
        sorter.setRowFilter(filter);
        
        // 模拟过滤后的行数
        when(excelTable.getRowCount()).thenReturn(10000); // 过滤后剩余10000行
        
        // 调用同步方法
        frozenColumnTable.syncAfterFilter();
        
        // 验证行号表格的行数是否正确同步
        assertEquals(10000, frozenColumnTable.getVisibleRowCount(),
                "过滤后行号表格的可见行数应该与主表格一致");
    }
    
    @Test
    void testLargeDataScrollSync() {
        // 测试大数据量下的滚动同步
        JTable rowHeaderTable = frozenColumnTable.getRowHeaderTable();
        
        // 验证行号表格能够处理大数据量
        assertTrue(rowHeaderTable.getRowCount() >= 10000,
                "行号表格应该能够处理超过10000行的数据");
        
        // 测试行索引转换
        for (int i = 0; i < Math.min(1000, rowHeaderTable.getRowCount()); i++) {
            int modelRow = frozenColumnTable.convertRowIndexToModel(i);
            int viewRow = frozenColumnTable.convertRowIndexToView(modelRow);
            assertEquals(i, viewRow, "行索引转换应该保持一致性");
        }
    }
    
    @Test
    void testRowHeaderDataConsistency() {
        // 测试行号数据的一致性
        JTable rowHeaderTable = frozenColumnTable.getRowHeaderTable();
        
        // 验证行号显示正确
        for (int i = 0; i < Math.min(100, rowHeaderTable.getRowCount()); i++) {
            Object rowNumber = rowHeaderTable.getModel().getValueAt(i, 0);
            assertEquals(i + 1, rowNumber, "行号应该从1开始递增");
        }
    }
    
    /**
     * 测试用的表格模型
     */
    private static class TestTableModel extends AbstractTableModel {
        private final int rowCount;
        private final String[] columnNames = {"选择", "列1", "列2", "列3"};
        
        public TestTableModel(int rowCount) {
            this.rowCount = rowCount;
        }
        
        @Override
        public int getRowCount() {
            return rowCount;
        }
        
        @Override
        public int getColumnCount() {
            return columnNames.length;
        }
        
        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            if (columnIndex == 0) {
                return false; // 复选框列
            }
            return "数据" + rowIndex + "-" + columnIndex;
        }
        
        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }
        
        @Override
        public Class<?> getColumnClass(int columnIndex) {
            if (columnIndex == 0) {
                return Boolean.class;
            }
            return String.class;
        }
        
        @Override
        public boolean isCellEditable(int rowIndex, int columnIndex) {
            return columnIndex == 0; // 只有复选框列可编辑
        }
        
        @Override
        public void setValueAt(Object value, int rowIndex, int columnIndex) {
            if (columnIndex == 0) {
                // 模拟复选框值设置
                fireTableCellUpdated(rowIndex, columnIndex);
            }
        }
    }
}
